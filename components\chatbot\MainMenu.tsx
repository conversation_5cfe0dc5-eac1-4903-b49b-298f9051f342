import React from 'react';
import { ScreenSize } from './types';

interface MenuOption {
  id: string;
  icon: string;
  text: string;
  description: string;
  message: string;
  intent: string;
}

interface MainMenuProps {
  onMenuClick: (message: string, intent: string) => void;
  screenSize: ScreenSize;
}

export default function MainMenu({ onMenuClick, screenSize }: MainMenuProps) {
  const isSmall = screenSize?.isSmall || false;
  const width = screenSize?.width || 1200;
  
  // Adjust sizing based on screen size
  let buttonPadding = 'px-4 py-3';
  let buttonText = 'text-sm';
  let spacing = 'gap-3';
  let titleText = 'text-lg';

  if (isSmall) {
    if (width <= 320) {
      buttonPadding = 'px-3 py-2';
      buttonText = 'text-xs';
      spacing = 'gap-2';
      titleText = 'text-base';
    } else if (width <= 375) {
      buttonPadding = 'px-3 py-2';
      buttonText = 'text-xs';
      spacing = 'gap-2';
      titleText = 'text-base';
    } else {
      buttonPadding = 'px-4 py-3';
      buttonText = 'text-sm';
      spacing = 'gap-3';
      titleText = 'text-lg';
    }
  }

  const menuOptions: MenuOption[] = [
    {
      id: 'prices',
      icon: '💰',
      text: 'View Pricing',
      description: 'View our pricing and packages',
      message: 'I want to know about your prices',
      intent: 'prices'
    },
    {
      id: 'booking',
      icon: '📅',
      text: 'Book Meeting',
      description: 'Schedule a free consultation',
      message: 'I want to book a meeting',
      intent: 'booking'
    },
    {
      id: 'services',
      icon: '🚀',
      text: 'Our Services',
      description: 'Learn about our solutions',
      message: 'Tell me about your services',
      intent: 'services'
    },
    {
      id: 'human_help',
      icon: '💬',
      text: 'Talk to Human',
      description: 'Get personalized help',
      message: 'I need to speak with a human',
      intent: 'detailed_support'
    }
  ];

  const handleMenuClick = (option: MenuOption) => {
    onMenuClick(option.message, option.intent);
  };

  return (
    <div className="w-full animate-fade-in mb-4">
      <div className="bg-gradient-to-br from-blue-50 to-purple-50 rounded-lg p-4 border border-blue-100">
        <h3 className={`${titleText} font-bold text-gray-800 mb-4 text-center`}>
          How can I help you today?
        </h3>
        <div className={`grid grid-cols-2 ${spacing}`}>
          {menuOptions.map((option) => (
            <button
              key={option.id}
              onClick={() => handleMenuClick(option)}
              className={`
                ${buttonPadding}
                bg-gradient-to-r from-purple-500 to-pink-500
                text-white rounded-full font-medium
                hover:from-purple-600 hover:to-pink-600
                transition-all duration-200
                shadow-md hover:shadow-lg
                transform hover:scale-105
                ${buttonText}
                flex items-center justify-center
                min-h-[44px]
              `}
            >
              <span className="mr-2">{option.icon}</span>
              {option.text}
            </button>
          ))}
        </div>
      </div>
    </div>
  );
}
