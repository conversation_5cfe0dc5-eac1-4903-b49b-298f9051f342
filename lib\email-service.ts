import FormData from 'form-data';
import Mailgun from 'mailgun.js';

export interface EmailOptions {
  to: string | string[];
  subject: string;
  html: string;
  from?: string;
}

export interface EmailServiceConfig {
  apiKey: string;
  domain: string;
  from: string;
  region?: 'us' | 'eu';
}

class EmailService {
  private mg: any;
  private config: EmailServiceConfig;

  constructor(config: EmailServiceConfig) {
    this.config = config;
    const mailgun = new Mailgun(FormData);
    
    this.mg = mailgun.client({
      username: 'api',
      key: config.apiKey,
      url: config.region === 'eu' ? 'https://api.eu.mailgun.net' : 'https://api.mailgun.net'
    });
  }

  async sendEmail(options: EmailOptions): Promise<any> {
    try {
      const emailData = {
        from: options.from || this.config.from,
        to: Array.isArray(options.to) ? options.to : [options.to],
        subject: options.subject,
        html: options.html,
      };

      console.log('Sending email via Mailgun:', {
        domain: this.config.domain,
        to: emailData.to,
        subject: emailData.subject,
        from: emailData.from
      });

      const result = await this.mg.messages.create(this.config.domain, emailData);
      console.log('Email sent successfully:', result);
      return result;
    } catch (error) {
      console.error('Failed to send email via Mailgun:', error);
      throw new Error(`Failed to send email: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  async sendMultipleEmails(emails: EmailOptions[]): Promise<any[]> {
    try {
      const results = await Promise.all(
        emails.map(email => this.sendEmail(email))
      );
      return results;
    } catch (error) {
      console.error('Failed to send multiple emails:', error);
      throw error;
    }
  }
}

// Create a singleton instance
let emailService: EmailService | null = null;

export function getEmailService(): EmailService {
  if (!emailService) {
    const config: EmailServiceConfig = {
      apiKey: process.env.MAILGUN_API_KEY || '',
      domain: process.env.MAILGUN_DOMAIN || '',
      from: process.env.MAILGUN_FROM || 'UpZera <<EMAIL>>',
      region: (process.env.MAILGUN_REGION as 'us' | 'eu') || 'eu'
    };

    if (!config.apiKey || !config.domain) {
      throw new Error('Mailgun configuration is missing. Please set MAILGUN_API_KEY and MAILGUN_DOMAIN environment variables.');
    }

    emailService = new EmailService(config);
  }

  return emailService;
}

// Email template utilities
export function createContactFormNotificationEmail(data: {
  name: string;
  email: string;
  service: string;
  message: string;
}): string {
  return `
    <div style="max-width:600px; margin:0 auto; font-family:'Arial', sans-serif; background:#ffffff; border:1px solid #e5e7eb; border-radius:12px; overflow:hidden; box-shadow:0 4px 6px rgba(0,0,0,0.05);">
      <!-- Header with Logo -->
      <div style="background: linear-gradient(135deg, #7e22ce, #9333ea); padding:28px; text-align:center; border-bottom:1px solid #a855f7;">
        <img src="https://upzera-web.netlify.app/logo/UpZera_logo_3-nobkgr.png" alt="UpZera Logo" style="max-height:70px; filter: brightness(0) invert(1);">
      </div>

      <!-- Content -->
      <div style="padding:32px;">
        <h1 style="color:#7e22ce; font-size:24px; font-weight:bold; margin:0 0 24px 0; text-align:center;">
          New Contact Form Submission
        </h1>
        
        <div style="background:#f8fafc; padding:24px; border-radius:8px; margin-bottom:24px; border-left:4px solid #7e22ce;">
          <h2 style="color:#374151; font-size:18px; margin:0 0 16px 0;">Contact Details</h2>
          <p style="margin:8px 0; color:#4b5563;"><strong style="color:#374151;">Name:</strong> ${data.name}</p>
          <p style="margin:8px 0; color:#4b5563;"><strong style="color:#374151;">Email:</strong> ${data.email}</p>
          <p style="margin:8px 0; color:#4b5563;"><strong style="color:#374151;">Service:</strong> ${data.service}</p>
        </div>

        <div style="background:#f8fafc; padding:24px; border-radius:8px; border-left:4px solid #7e22ce;">
          <h2 style="color:#374151; font-size:18px; margin:0 0 16px 0;">Message</h2>
          <p style="color:#4b5563; line-height:1.6; margin:0; white-space:pre-wrap;">${data.message}</p>
        </div>
      </div>

      <!-- Footer -->
      <div style="background:#f5f3ff; padding:18px; text-align:center; border-top:1px solid #e9d5ff; color:#6b21a8; font-size:13px;">
        © ${new Date().getFullYear()} UpZera. All rights reserved.
      </div>
    </div>
  `;
}

export function createContactFormConfirmationEmail(name: string): string {
  return `
    <div style="max-width:600px; margin:0 auto; font-family:'Arial', sans-serif; background:#ffffff; border:1px solid #e5e7eb; border-radius:12px; overflow:hidden; box-shadow:0 4px 6px rgba(0,0,0,0.05);">
      <!-- Header with Logo -->
      <div style="background: linear-gradient(135deg, #7e22ce, #9333ea); padding:28px; text-align:center; border-bottom:1px solid #a855f7;">
        <img src="https://upzera-web.netlify.app/logo/UpZera_logo_3-nobkgr.png" alt="UpZera Logo" style="max-height:70px; filter: brightness(0) invert(1);">
      </div>

      <!-- Content -->
      <div style="padding:32px;">
        <h1 style="color:#7e22ce; font-size:24px; font-weight:bold; margin:0 0 24px 0; text-align:center;">
          Thank You for Contacting Us!
        </h1>
        
        <p style="color:#4b5563; line-height:1.6; margin:0 0 20px 0; font-size:16px;">
          Dear ${name},
        </p>
        
        <p style="color:#4b5563; line-height:1.6; margin:0 0 20px 0; font-size:16px;">
          Thank you for reaching out to UpZera! We have received your message and our team will review it carefully.
        </p>
        
        <p style="color:#4b5563; line-height:1.6; margin:0 0 20px 0; font-size:16px;">
          We typically respond within 24-48 hours during business days. If your inquiry is urgent, please don't hesitate to call us directly.
        </p>
        
        <div style="background:#f8fafc; padding:24px; border-radius:8px; margin:24px 0; border-left:4px solid #7e22ce; text-align:center;">
          <p style="color:#7e22ce; font-weight:bold; margin:0 0 8px 0; font-size:16px;">Need immediate assistance?</p>
          <p style="color:#4b5563; margin:0; font-size:14px;">Contact <NAME_EMAIL></p>
        </div>
        
        <p style="color:#4b5563; line-height:1.6; margin:0; font-size:16px;">
          Best regards,<br>
          <strong style="color:#7e22ce;">The UpZera Team</strong>
        </p>
      </div>

      <!-- Footer -->
      <div style="background:#f5f3ff; padding:18px; text-align:center; border-top:1px solid #e9d5ff; color:#6b21a8; font-size:13px;">
        © ${new Date().getFullYear()} UpZera. All rights reserved.
      </div>
    </div>
  `;
}

export function createNewsletterNotificationEmail(email: string): string {
  return `
    <div style="max-width:600px; margin:0 auto; font-family:'Arial', sans-serif; background:#ffffff; border:1px solid #e5e7eb; border-radius:12px; overflow:hidden; box-shadow:0 4px 6px rgba(0,0,0,0.05);">
      <!-- Header with Logo -->
      <div style="background: linear-gradient(135deg, #7e22ce, #9333ea); padding:28px; text-align:center; border-bottom:1px solid #a855f7;">
        <img src="https://upzera-web.netlify.app/logo/UpZera_logo_3-nobkgr.png" alt="UpZera Logo" style="max-height:70px; filter: brightness(0) invert(1);">
      </div>

      <!-- Content -->
      <div style="padding:32px;">
        <h1 style="color:#7e22ce; font-size:24px; font-weight:bold; margin:0 0 24px 0; text-align:center;">
          New Newsletter Subscription
        </h1>
        
        <div style="background:#f8fafc; padding:24px; border-radius:8px; border-left:4px solid #7e22ce;">
          <p style="color:#4b5563; line-height:1.6; margin:0; font-size:16px;">
            <strong style="color:#374151;">Email:</strong> ${email}
          </p>
          <p style="color:#6b7280; margin:12px 0 0 0; font-size:14px;">
            Subscribed on: ${new Date().toLocaleDateString('en-US', { 
              year: 'numeric', 
              month: 'long', 
              day: 'numeric',
              hour: '2-digit',
              minute: '2-digit'
            })}
          </p>
        </div>
      </div>

      <!-- Footer -->
      <div style="background:#f5f3ff; padding:18px; text-align:center; border-top:1px solid #e9d5ff; color:#6b21a8; font-size:13px;">
        © ${new Date().getFullYear()} UpZera. All rights reserved.
      </div>
    </div>
  `;
}

export function createNewsletterConfirmationEmail(): string {
  return `
    <div style="max-width:600px; margin:0 auto; font-family:'Arial', sans-serif; background:#ffffff; border:1px solid #e5e7eb; border-radius:12px; overflow:hidden; box-shadow:0 4px 6px rgba(0,0,0,0.05);">
      <!-- Header with Logo -->
      <div style="background: linear-gradient(135deg, #7e22ce, #9333ea); padding:28px; text-align:center; border-bottom:1px solid #a855f7;">
        <img src="https://upzera-web.netlify.app/logo/UpZera_logo_3-nobkgr.png" alt="UpZera Logo" style="max-height:70px; filter: brightness(0) invert(1);">
      </div>

      <!-- Content -->
      <div style="padding:32px;">
        <h1 style="color:#7e22ce; font-size:24px; font-weight:bold; margin:0 0 24px 0; text-align:center;">
          Welcome to UpZera Newsletter!
        </h1>
        
        <p style="color:#4b5563; line-height:1.6; margin:0 0 20px 0; font-size:16px;">
          Thank you for subscribing to our newsletter! You'll now receive the latest updates about our services, industry insights, and exclusive offers.
        </p>
        
        <div style="background:#f8fafc; padding:24px; border-radius:8px; margin:24px 0; border-left:4px solid #7e22ce;">
          <p style="color:#7e22ce; font-weight:bold; margin:0 0 8px 0; font-size:16px;">What to expect:</p>
          <ul style="color:#4b5563; margin:0; padding-left:20px; font-size:14px;">
            <li style="margin-bottom:8px;">Weekly industry insights and trends</li>
            <li style="margin-bottom:8px;">Exclusive offers and early access to new services</li>
            <li style="margin-bottom:8px;">Tips and best practices from our experts</li>
            <li>Company updates and announcements</li>
          </ul>
        </div>
        
        <p style="color:#4b5563; line-height:1.6; margin:0; font-size:16px;">
          Best regards,<br>
          <strong style="color:#7e22ce;">The UpZera Team</strong>
        </p>
      </div>

      <!-- Footer -->
      <div style="background:#f5f3ff; padding:18px; text-align:center; border-top:1px solid #e9d5ff; color:#6b21a8; font-size:13px;">
        © ${new Date().getFullYear()} UpZera. All rights reserved.
        <p style="margin-top:8px; font-size:11px; color:#6b21a8;">
          You're receiving this email because you signed up for our newsletter. 
          If you'd like to unsubscribe, please reply to this email with "Unsubscribe" in the subject line.
        </p>
      </div>
    </div>
  `;
}

// Support Ticket Email Templates
export function createSupportTicketNotificationEmail(data: {
  ticketNumber: string;
  name: string;
  email: string;
  problemDescription: string;
  conversationHistory: string;
}): string {
  return `
    <div style="max-width:600px; margin:0 auto; font-family:'Arial', sans-serif; background:#ffffff; border:1px solid #e5e7eb; border-radius:12px; overflow:hidden; box-shadow:0 4px 6px rgba(0,0,0,0.05);">
      <!-- Header with Logo -->
      <div style="background: linear-gradient(135deg, #dc2626, #ef4444); padding:28px; text-align:center; border-bottom:1px solid #f87171;">
        <img src="https://upzera-web.netlify.app/logo/UpZera_logo_3-nobkgr.png" alt="UpZera Logo" style="max-height:70px; filter: brightness(0) invert(1);">
      </div>

      <!-- Main Content -->
      <div style="padding:32px; background:#ffffff;">
        <h1 style="color:#dc2626; font-size:28px; font-weight:bold; margin:0 0 20px 0; text-align:center;">🎫 New Support Ticket</h1>

        <!-- Ticket Info Box -->
        <div style="background:#fef2f2; border:1px solid #fecaca; border-radius:8px; padding:20px; margin:0 0 24px 0;">
          <h2 style="color:#dc2626; font-size:20px; font-weight:bold; margin:0 0 12px 0;">Ticket #${data.ticketNumber}</h2>
          <p style="color:#7f1d1d; font-size:14px; margin:0;">Created: ${new Date().toLocaleString()}</p>
        </div>

        <!-- Customer Details -->
        <div style="margin:0 0 24px 0;">
          <h3 style="color:#1f2937; font-size:18px; font-weight:600; margin:0 0 12px 0;">Customer Details</h3>
          <div style="background:#f9fafb; border-radius:6px; padding:16px;">
            <p style="color:#374151; font-size:16px; margin:0 0 8px 0;"><strong>Name:</strong> ${data.name}</p>
            <p style="color:#374151; font-size:16px; margin:0;"><strong>Email:</strong> ${data.email}</p>
          </div>
        </div>

        <!-- Problem Description -->
        <div style="margin:0 0 24px 0;">
          <h3 style="color:#1f2937; font-size:18px; font-weight:600; margin:0 0 12px 0;">Problem Description</h3>
          <div style="background:#f9fafb; border-radius:6px; padding:16px; border-left:4px solid #dc2626;">
            <p style="color:#374151; font-size:16px; line-height:1.6; margin:0;">${data.problemDescription}</p>
          </div>
        </div>

        ${data.conversationHistory ? `
        <!-- Conversation History -->
        <div style="margin:0 0 24px 0;">
          <h3 style="color:#1f2937; font-size:18px; font-weight:600; margin:0 0 12px 0;">Conversation History</h3>
          <div style="background:#f8fafc; border:1px solid #e2e8f0; border-radius:8px; padding:20px; max-height:300px; overflow-y:auto;">
            <pre style="color:#475569; font-size:13px; line-height:1.6; margin:0; white-space:pre-wrap; font-family:'Courier New', monospace; background:transparent;">${data.conversationHistory}</pre>
          </div>
        </div>
        ` : ''}

        <!-- Action Required -->
        <div style="background:#fef3c7; border:1px solid #fbbf24; border-radius:8px; padding:20px; margin:24px 0;">
          <h3 style="color:#92400e; font-size:16px; font-weight:600; margin:0 0 8px 0;">⚡ Action Required</h3>
          <p style="color:#92400e; font-size:14px; margin:0;">
            Please respond to this support ticket as soon as possible. The customer is expecting a response.
          </p>
        </div>
      </div>

      <!-- Footer -->
      <div style="background:#f9fafb; padding:24px; text-align:center; border-top:1px solid #e5e7eb;">
        <p style="color:#6b7280; font-size:14px; margin:0 0 8px 0;">
          UpZera Support System
        </p>
        <p style="color:#9ca3af; font-size:12px; margin:0;">
          This is an automated notification. Please respond directly to the customer's email.
        </p>
      </div>
    </div>
  `;
}

export function createSupportTicketConfirmationEmail(data: {
  name: string;
  ticketNumber: string;
}): string {
  return `
    <div style="max-width:600px; margin:0 auto; font-family:'Arial', sans-serif; background:#ffffff; border:1px solid #e5e7eb; border-radius:12px; overflow:hidden; box-shadow:0 4px 6px rgba(0,0,0,0.05);">
      <!-- Header with Logo -->
      <div style="background: linear-gradient(135deg, #059669, #10b981); padding:28px; text-align:center; border-bottom:1px solid #34d399;">
        <img src="https://upzera-web.netlify.app/logo/UpZera_logo_3-nobkgr.png" alt="UpZera Logo" style="max-height:70px; filter: brightness(0) invert(1);">
      </div>

      <!-- Main Content -->
      <div style="padding:32px; background:#ffffff;">
        <h1 style="color:#059669; font-size:28px; font-weight:bold; margin:0 0 20px 0; text-align:center;">✅ Support Ticket Created</h1>

        <p style="color:#4b5563; font-size:16px; line-height:1.6; margin:0 0 20px 0;">
          Hi ${data.name},
        </p>

        <p style="color:#4b5563; font-size:16px; line-height:1.6; margin:0 0 20px 0;">
          Thank you for reaching out to us! We've successfully created a support ticket for your request.
        </p>

        <!-- Ticket Info Box -->
        <div style="background:#ecfdf5; border:1px solid #a7f3d0; border-radius:8px; padding:20px; margin:0 0 24px 0; text-align:center;">
          <h2 style="color:#059669; font-size:24px; font-weight:bold; margin:0 0 8px 0;">Your Ticket Number</h2>
          <div style="background:#ffffff; border:2px solid #059669; border-radius:6px; padding:12px; display:inline-block;">
            <span style="color:#059669; font-size:20px; font-weight:bold; font-family:monospace;">#${data.ticketNumber}</span>
          </div>
          <p style="color:#065f46; font-size:14px; margin:12px 0 0 0;">Please save this number for your records</p>
        </div>

        <!-- What Happens Next -->
        <div style="margin:0 0 24px 0;">
          <h3 style="color:#1f2937; font-size:18px; font-weight:600; margin:0 0 16px 0;">What happens next?</h3>
          <div style="background:#f9fafb; border-radius:6px; padding:20px;">
            <div style="display:flex; align-items:flex-start; margin-bottom:16px;">
              <div style="background:#059669; color:#ffffff; border-radius:50%; width:24px; height:24px; display:flex; align-items:center; justify-content:center; font-weight:bold; font-size:12px; margin-right:12px; flex-shrink:0;">1</div>
              <p style="color:#374151; font-size:14px; margin:0; line-height:1.5;">Our support team will review your request within 2-4 business hours</p>
            </div>
            <div style="display:flex; align-items:flex-start; margin-bottom:16px;">
              <div style="background:#059669; color:#ffffff; border-radius:50%; width:24px; height:24px; display:flex; align-items:center; justify-content:center; font-weight:bold; font-size:12px; margin-right:12px; flex-shrink:0;">2</div>
              <p style="color:#374151; font-size:14px; margin:0; line-height:1.5;">A team member will reach out to you directly via email</p>
            </div>
            <div style="display:flex; align-items:flex-start;">
              <div style="background:#059669; color:#ffffff; border-radius:50%; width:24px; height:24px; display:flex; align-items:center; justify-content:center; font-weight:bold; font-size:12px; margin-right:12px; flex-shrink:0;">3</div>
              <p style="color:#374151; font-size:14px; margin:0; line-height:1.5;">We'll work together to resolve your issue quickly and efficiently</p>
            </div>
          </div>
        </div>

        <!-- Contact Info -->
        <div style="background:#fef3c7; border:1px solid #fbbf24; border-radius:8px; padding:20px; margin:24px 0;">
          <h3 style="color:#92400e; font-size:16px; font-weight:600; margin:0 0 8px 0;">Need immediate assistance?</h3>
          <p style="color:#92400e; font-size:14px; margin:0 0 8px 0;">
            If your issue is urgent, you can also reach us directly at:
          </p>
          <p style="color:#92400e; font-size:14px; margin:0;">
            📧 <strong><EMAIL></strong>
          </p>
        </div>

        <p style="color:#4b5563; font-size:16px; line-height:1.6; margin:24px 0 0 0;">
          Thank you for choosing UpZera. We appreciate your business and look forward to helping you!
        </p>

        <p style="color:#4b5563; font-size:16px; line-height:1.6; margin:16px 0 0 0;">
          Best regards,<br>
          <strong style="color:#059669;">The UpZera Support Team</strong>
        </p>
      </div>

      <!-- Footer -->
      <div style="background:#f9fafb; padding:24px; text-align:center; border-top:1px solid #e5e7eb;">
        <p style="color:#6b7280; font-size:14px; margin:0 0 8px 0;">
          UpZera - Building Smart Digital Solutions
        </p>
        <p style="color:#9ca3af; font-size:12px; margin:0;">
          This is an automated confirmation. Please do not reply to this email.
        </p>
      </div>
    </div>
  `;
}
