// Conversation State Management
export const ConversationStates = {
  WELCOME: 'welcome',
  MAIN_MENU: 'main_menu',
  PRICES: 'prices',
  BOOKING: 'booking',
  SERVICES: 'services',
  SERVICE_SELECTION: 'service_selection',
  HUMAN_HANDOFF: 'human_handoff',
  COLLECT_NAME: 'collect_name',
  COLLECT_EMAIL: 'collect_email',
  CONFIRM_DETAILS: 'confirm_details',
  ANYTHING_ELSE: 'anything_else',
  CONVERSATION_ENDED: 'conversation_ended',
  SUPPORT_TICKET_NAME: 'support_ticket_name',
  SUPPORT_TICKET_EMAIL: 'support_ticket_email',
  SUPPORT_TICKET_DESCRIPTION: 'support_ticket_description',
  SUPPORT_TICKET_CONFIRM: 'support_ticket_confirm'
} as const;

export type ConversationState = typeof ConversationStates[keyof typeof ConversationStates];

// Timeout configurations
export const TIMEOUT_DURATION = 5 * 60 * 1000; // 5 minutes
export const WARNING_DURATION = 4 * 60 * 1000; // 4 minutes (1 minute before timeout)

// Screen size breakpoints
export const SCREEN_BREAKPOINTS = {
  EXTRA_SMALL: 320,
  SMALL: 375,
  MEDIUM: 768
} as const;
